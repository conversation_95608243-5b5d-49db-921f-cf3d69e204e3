# 微信登录功能测试指南

## 当前状态

✅ **API连接正常** - 服务器响应正常，PHP 8.0.30
✅ **微信登录code获取成功** - 微信授权正常
✅ **微信API调用成功** - 成功获取openid
✅ **need_bind状态正确** - 正确识别未绑定账号

## 已修复的问题

1. **逻辑处理错误** - 修复了need_bind状态的处理逻辑
2. **Token管理** - 添加了登录和绑定成功后的token生成
3. **调试信息** - 增强了调试日志输出

## 测试步骤

### 第一步：基础功能测试

在"微信登录测试"页面按顺序测试：

1. **API连接测试** ✅
   - 应该显示：API连接正常
   - 显示服务器时间和微信AppID

2. **数据库检查** ✅
   - 应该显示：数据库表完整

3. **数据库初始化**
   - 应该显示：数据库表已存在，无需初始化

### 第二步：微信登录流程测试

4. **微信登录测试**
   - 点击"微信登录测试"
   - 应该显示：需要绑定账号
   - 应该显示绑定数据（包含openid和wechat_user_id）

5. **账号绑定测试**
   - 点击"账号绑定测试 (test/password)"
   - 应该显示：绑定成功
   - 应该显示：登录状态变为已登录

### 第三步：完整用户流程测试

6. **在"我的"页面测试**
   - 退出登录（如果已登录）
   - 点击头像区域
   - 点击"微信登录"
   - 应该弹出绑定弹窗
   - 输入：用户名 `test`，密码 `password`
   - 点击绑定
   - 应该登录成功并显示用户信息

### 第四步：再次登录测试

7. **测试已绑定用户登录**
   - 退出登录
   - 再次点击"微信登录"
   - 应该直接登录成功，不需要再次绑定

## 预期结果

### 微信登录测试
```
需要绑定账号
绑定数据: {"openid":"oPkSw67-LZZTiN7QtrLC_245Ag7A","wechat_user_id":"1","nickname":""}
```

### 账号绑定测试
```
绑定成功
登录状态: 已登录
用户信息: 用户: test, 角色: user
```

### 完整登录流程
- 弹出绑定弹窗
- 输入test/password
- 绑定成功
- 显示用户信息：test (普通用户)

### 再次登录
- 直接登录成功
- 不需要再次绑定

## 测试账号

- **用户名**: `test`
- **密码**: `password`
- **角色**: 普通用户

- **用户名**: `admin`  
- **密码**: `password`
- **角色**: 管理员

## 故障排除

### 如果微信登录测试失败
1. 检查控制台是否有错误日志
2. 确认微信code获取成功
3. 确认API响应状态码为200

### 如果绑定测试失败
1. 确认用户名密码正确
2. 检查数据库连接
3. 查看API错误日志

### 如果"我的"页面登录失败
1. 确认测试页面的绑定测试成功
2. 检查profile页面的逻辑
3. 查看控制台错误信息

## 技术细节

### API响应格式

**微信登录（需要绑定）**:
```json
{
  "status": "need_bind",
  "message": "微信账号尚未绑定系统账号，请先绑定",
  "data": {
    "openid": "oPkSw67-LZZTiN7QtrLC_245Ag7A",
    "wechat_user_id": "1",
    "nickname": ""
  }
}
```

**账号绑定成功**:
```json
{
  "status": "success",
  "message": "绑定成功",
  "data": {
    "token": "abc123...",
    "expires_at": "2025-08-16 20:50:00",
    "user": {
      "id": 1,
      "username": "test",
      "role": "user",
      "is_station_staff": 0
    }
  }
}
```

**微信登录（已绑定）**:
```json
{
  "status": "success",
  "message": "登录成功",
  "data": {
    "token": "def456...",
    "expires_at": "2025-08-16 20:50:00",
    "user": {
      "id": 1,
      "username": "test",
      "role": "user",
      "is_station_staff": 0
    },
    "wechat": {
      "openid": "oPkSw67-L...",
      "nickname": ""
    }
  }
}
```

## 下一步

完成所有测试后，微信登录功能就完全可用了。用户可以：

1. 首次使用时绑定系统账号
2. 后续直接微信登录
3. 在"我的"页面查看用户信息
4. 正常退出登录

现在请按照测试步骤进行完整测试！
