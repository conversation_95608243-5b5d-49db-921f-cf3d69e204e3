/**
 * 微信小程序登录功能
 * 基于shili示例代码集成
 */

// 配置API基础URL
const API_BASE_URL = 'https://sunxiyue.com/zdh/api/wechat_login_api.php';

// 调试模式
const DEBUG_MODE = true;

/**
 * 微信登录管理类
 */
class WechatLoginManager {
  constructor() {
    this.token = wx.getStorageSync('wechat_token') || '';
    this.userInfo = wx.getStorageSync('user_info') || null;
  }

  /**
   * 微信登录（使用已获取的用户信息）
   */
  async wechatLoginWithUserInfo(userInfo = null) {
    try {
      // 1. 获取微信授权码
      const loginRes = await this.wxLogin();
      console.log('微信登录结果:', loginRes);

      // 2. 调用后端登录接口
      console.log('准备调用API，参数:', { code: loginRes.code, userInfo: userInfo });
      const response = await this.callAPI('wechat_login', {
        code: loginRes.code,
        userInfo: userInfo
      });

      console.log('API响应完整数据:', response);
      console.log('API响应状态:', response.status);

      if (response.status === 'success') {
        // 登录成功，保存token和用户信息
        if (response.data.token) {
          this.token = response.data.token;
          wx.setStorageSync('wechat_token', this.token);
          wx.setStorageSync('token_expires_at', response.data.expires_at);
        }
        
        this.userInfo = response.data.user;
        wx.setStorageSync('user_info', this.userInfo);

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });

        return {
          success: true,
          data: response.data
        };

      } else if (response.status === 'need_bind') {
        // 需要绑定账号
        if (DEBUG_MODE) {
          console.log('需要绑定账号，返回绑定数据:', response.data);
        }
        
        return {
          success: false,
          needBind: true,
          data: response.data
        };

      } else {
        throw new Error(response.message || '登录失败');
      }

    } catch (error) {
      console.error('微信登录失败:', error);
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      });
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 微信登录（旧版本，保持兼容性）
   */
  async wechatLogin(getUserInfo = false) {
    try {
      // 1. 获取微信授权码
      const loginRes = await this.wxLogin();
      console.log('微信登录结果:', loginRes);

      // 2. 获取用户信息（可选，只有在用户主动点击时才获取）
      let userInfo = null;
      if (getUserInfo) {
        try {
          userInfo = await this.getUserProfile();
          console.log('获取用户信息成功:', userInfo);
        } catch (e) {
          console.log('用户取消授权或获取用户信息失败:', e);
          // 用户取消授权，但仍然可以继续登录流程
        }
      }

      // 3. 调用后端登录接口
      console.log('准备调用API，参数:', { code: loginRes.code, userInfo: userInfo });
      const response = await this.callAPI('wechat_login', {
        code: loginRes.code,
        userInfo: userInfo
      });

      console.log('API响应完整数据:', response);
      console.log('API响应状态:', response.status);

      if (response.status === 'success') {
        // 登录成功，保存token和用户信息
        if (response.data.token) {
          this.token = response.data.token;
          wx.setStorageSync('wechat_token', this.token);
          wx.setStorageSync('token_expires_at', response.data.expires_at);
        }
        
        this.userInfo = response.data.user;
        wx.setStorageSync('user_info', this.userInfo);

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });

        return {
          success: true,
          data: response.data
        };

      } else if (response.status === 'need_bind') {
        // 需要绑定账号
        if (DEBUG_MODE) {
          console.log('需要绑定账号，返回绑定数据:', response.data);
        }
        
        return {
          success: false,
          needBind: true,
          data: response.data
        };

      } else {
        throw new Error(response.message || '登录失败');
      }

    } catch (error) {
      console.error('微信登录失败:', error);
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      });
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 绑定系统账号
   */
  async bindAccount(bindData, username, password) {
    try {
      const response = await this.callAPI('bind_account', {
        openid: bindData.openid,
        wechat_user_id: bindData.wechat_user_id,
        username: username,
        password: password
      });

      if (response.status === 'success') {
        // 绑定成功，保存token和用户信息
        this.token = response.data.token;
        this.userInfo = response.data.user;
        
        wx.setStorageSync('wechat_token', this.token);
        wx.setStorageSync('user_info', this.userInfo);
        wx.setStorageSync('token_expires_at', response.data.expires_at);

        wx.showToast({
          title: '绑定成功',
          icon: 'success'
        });

        return {
          success: true,
          data: response.data
        };
      } else {
        throw new Error(response.message || '绑定失败');
      }

    } catch (error) {
      console.error('账号绑定失败:', error);
      wx.showToast({
        title: error.message || '绑定失败',
        icon: 'none'
      });
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 验证Token有效性
   */
  async verifyToken() {
    if (!this.token) {
      return false;
    }

    try {
      const response = await this.callAPI('verify_token', {
        token: this.token
      });

      if (response.status === 'success') {
        // 更新用户信息
        this.userInfo = response.data.user;
        wx.setStorageSync('user_info', this.userInfo);
        return true;
      } else {
        // Token无效，清除本地存储
        this.clearLoginData();
        return false;
      }

    } catch (error) {
      console.error('Token验证失败:', error);
      this.clearLoginData();
      return false;
    }
  }

  /**
   * 解绑账号
   */
  async unbindAccount() {
    try {
      const response = await this.callAPI('unbind_account', {
        token: this.token
      });

      if (response.status === 'success') {
        this.clearLoginData();
        wx.showToast({
          title: '解绑成功',
          icon: 'success'
        });
        return true;
      } else {
        throw new Error(response.message || '解绑失败');
      }

    } catch (error) {
      console.error('解绑失败:', error);
      wx.showToast({
        title: error.message || '解绑失败',
        icon: 'none'
      });
      return false;
    }
  }

  /**
   * 检查登录状态
   */
  isLoggedIn() {
    return !!(this.token && this.userInfo);
  }

  /**
   * 获取用户信息
   */
  getUserInfo() {
    return this.userInfo;
  }

  /**
   * 正式退出登录（撤销服务器端Token）
   */
  async logout() {
    if (this.token) {
      try {
        await this.callAPI('logout', {
          token: this.token
        });
        if (DEBUG_MODE) {
          console.log('服务器端Token已撤销');
        }
      } catch (error) {
        console.error('撤销服务器端Token失败:', error);
        // 即使服务器端撤销失败，也要清除本地数据
      }
    }
    
    this.clearLoginData();
    
    wx.showToast({
      title: '已退出登录',
      icon: 'success'
    });
  }

  /**
   * 清除登录数据（本地清理）
   */
  clearLoginData() {
    this.token = '';
    this.userInfo = null;
    wx.removeStorageSync('wechat_token');
    wx.removeStorageSync('user_info');
    wx.removeStorageSync('token_expires_at');
  }

  /**
   * 微信登录
   */
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  }

  /**
   * 获取用户信息
   */
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          resolve(res.userInfo);
        },
        fail: reject
      });
    });
  }

  /**
   * 调用API接口
   */
  async callAPI(action, data = {}) {
    return new Promise((resolve, reject) => {
      const requestData = {
        action: action,
        ...data
      };

      if (DEBUG_MODE) {
        console.log('API请求URL:', API_BASE_URL);
        console.log('API请求数据:', requestData);
      }

      wx.request({
        url: API_BASE_URL,
        method: 'POST',
        data: requestData,
        header: {
          'Content-Type': 'application/json',
          ...(this.token ? { 'Authorization': `Bearer ${this.token}` } : {})
        },
        success: (res) => {
          if (DEBUG_MODE) {
            console.log('API响应状态码:', res.statusCode);
            console.log('API响应数据:', res.data);
          }

          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            const errorMsg = res.data && res.data.message ? res.data.message : '请求失败';
            if (DEBUG_MODE) {
              console.error('API请求失败:', res.statusCode, errorMsg);
            }
            reject(new Error(`HTTP ${res.statusCode}: ${errorMsg}`));
          }
        },
        fail: (error) => {
          console.error('网络请求失败:', error);
          reject(new Error('网络请求失败: ' + (error.errMsg || '未知错误')));
        }
      });
    });
  }
}

// 导出登录管理器实例
const loginManager = new WechatLoginManager();

module.exports = {
  loginManager,
  WechatLoginManager
};
