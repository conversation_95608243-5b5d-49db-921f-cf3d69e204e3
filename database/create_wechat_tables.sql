-- 微信登录功能数据库表结构
-- 数据库: sunxiyue_zdh

-- 1. 微信用户表
CREATE TABLE IF NOT EXISTS `wechat_users` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `openid` varchar(100) NOT NULL COMMENT '微信openid',
    `unionid` varchar(100) DEFAULT NULL COMMENT '微信unionid',
    `session_key` varchar(100) DEFAULT NULL COMMENT '微信session_key',
    `nickname` varchar(100) DEFAULT NULL COMMENT '微信昵称',
    `avatar_url` varchar(500) DEFAULT NULL COMMENT '微信头像URL',
    `gender` tinyint(1) DEFAULT 0 COMMENT '性别：0未知，1男，2女',
    `city` varchar(50) DEFAULT NULL COMMENT '城市',
    `province` varchar(50) DEFAULT NULL COMMENT '省份',
    `country` varchar(50) DEFAULT NULL COMMENT '国家',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `openid` (`openid`),
    KEY `unionid` (`unionid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户信息表';

-- 2. 用户微信绑定关系表
CREATE TABLE IF NOT EXISTS `user_wechat_bindings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL COMMENT '系统用户ID',
    `wechat_user_id` int(11) NOT NULL COMMENT '微信用户ID',
    `bind_type` enum('manual','auto') DEFAULT 'manual' COMMENT '绑定类型：manual手动，auto自动',
    `bind_ip` varchar(45) DEFAULT NULL COMMENT '绑定时的IP地址',
    `is_active` tinyint(1) DEFAULT 1 COMMENT '是否有效：1有效，0无效',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `user_wechat_unique` (`user_id`, `wechat_user_id`),
    KEY `user_id` (`user_id`),
    KEY `wechat_user_id` (`wechat_user_id`),
    KEY `is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户微信绑定关系表';

-- 3. 微信登录Token表
CREATE TABLE IF NOT EXISTS `wechat_tokens` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `wechat_user_id` int(11) DEFAULT NULL COMMENT '微信用户ID',
    `token` varchar(100) NOT NULL COMMENT '登录Token',
    `login_type` enum('wechat','password') DEFAULT 'wechat' COMMENT '登录类型',
    `expires_at` timestamp NOT NULL COMMENT 'Token过期时间',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` text COMMENT '用户代理',
    `last_used_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '最后使用时间',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `token` (`token`),
    KEY `user_id` (`user_id`),
    KEY `expires_at` (`expires_at`),
    KEY `wechat_user_id` (`wechat_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信登录Token表';

-- 4. 微信登录日志表
CREATE TABLE IF NOT EXISTS `wechat_login_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `openid` varchar(100) DEFAULT NULL COMMENT '微信openid',
    `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
    `action` varchar(50) NOT NULL COMMENT '操作类型：login,bind,unbind,verify',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` text COMMENT '用户代理',
    `result` enum('success','failed') NOT NULL COMMENT '操作结果',
    `error_message` text COMMENT '错误信息',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `openid` (`openid`),
    KEY `user_id` (`user_id`),
    KEY `action` (`action`),
    KEY `result` (`result`),
    KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信登录日志表';

-- 5. 检查users表是否存在，如果不存在则创建基础版本
CREATE TABLE IF NOT EXISTS `users` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `password` varchar(255) NOT NULL COMMENT '密码',
    `role` enum('admin','manager','user') DEFAULT 'user' COMMENT '角色',
    `is_station_staff` tinyint(1) DEFAULT 0 COMMENT '是否一站人员',
    `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：1正常，0禁用',
    `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `username` (`username`),
    KEY `role` (`role`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';

-- 插入测试用户（如果不存在）
INSERT IGNORE INTO `users` (`username`, `password`, `role`, `is_station_staff`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1),
('test', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 0);

-- 创建索引优化查询性能
ALTER TABLE `user_wechat_bindings` 
ADD INDEX `idx_user_active` (`user_id`, `is_active`),
ADD INDEX `idx_wechat_active` (`wechat_user_id`, `is_active`);

ALTER TABLE `wechat_tokens`
ADD INDEX `idx_user_expires` (`user_id`, `expires_at`),
ADD INDEX `idx_token_expires` (`token`, `expires_at`);

-- 添加外键约束（可选，根据实际情况决定是否启用）
-- ALTER TABLE `user_wechat_bindings` 
-- ADD CONSTRAINT `fk_binding_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
-- ADD CONSTRAINT `fk_binding_wechat` FOREIGN KEY (`wechat_user_id`) REFERENCES `wechat_users` (`id`) ON DELETE CASCADE;

-- ALTER TABLE `wechat_tokens`
-- ADD CONSTRAINT `fk_token_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
-- ADD CONSTRAINT `fk_token_wechat` FOREIGN KEY (`wechat_user_id`) REFERENCES `wechat_users` (`id`) ON DELETE SET NULL;
