<view class="container">
  <!-- 登录状态显示 -->
  <view class="status-section">
    <view class="status-title">当前登录状态</view>
    <view class="status-info">
      <text class="status-text">状态: {{isLoggedIn ? '已登录' : '未登录'}}</text>
      <text class="status-text" wx:if="{{userInfo}}">用户: {{userInfo.username}}</text>
      <text class="status-text" wx:if="{{userInfo}}">角色: {{userInfo.role}}</text>
    </view>
  </view>

  <!-- 测试按钮 -->
  <view class="test-section">
    <view class="test-title">功能测试</view>
    <view class="test-buttons">
      <button class="test-btn" bindtap="testWechatLogin" loading="{{loginLoading}}">
        微信登录测试
      </button>
      <button class="test-btn" bindtap="testAccountBind">
        账号绑定测试 (test/password)
      </button>
      <button class="test-btn" bindtap="testAPIConnection">
        API连接测试
      </button>
      <button class="test-btn" bindtap="testDatabaseCheck">
        数据库检查
      </button>
      <button class="test-btn" bindtap="testDatabaseInit">
        数据库初始化
      </button>
      <button class="test-btn" bindtap="testTokenVerify">
        Token验证测试
      </button>
      <button class="test-btn" bindtap="testLogout">
        退出登录测试
      </button>
      <button class="test-btn secondary" bindtap="clearTestResults">
        清除测试结果
      </button>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="results-section">
    <view class="results-title">测试结果</view>
    <view class="results-list">
      <view class="result-item" wx:for="{{testResults}}" wx:key="index">
        <view class="result-time">{{item.time}}</view>
        <view class="result-content">
          <text class="result-action">{{item.action}}</text>
          <text class="result-text">{{item.result}}</text>
        </view>
      </view>
      <view class="empty-results" wx:if="{{testResults.length === 0}}">
        <text>暂无测试结果</text>
      </view>
    </view>
  </view>
</view>
