const { loginManager } = require('../../utils/login.js');

Page({
  data: {
    isLoggedIn: false,
    userInfo: null,
    loginLoading: false,
    testResults: [],
    lastBindData: null
  },

  onLoad() {
    this.checkLoginStatus();
  },

  async checkLoginStatus() {
    const isLoggedIn = loginManager.isLoggedIn();
    const userInfo = loginManager.getUserInfo();

    this.setData({
      isLoggedIn,
      userInfo: userInfo || null
    });

    this.addTestResult('检查登录状态', isLoggedIn ? '已登录' : '未登录');
    if (userInfo) {
      this.addTestResult('用户信息', `用户: ${userInfo.username}, 角色: ${userInfo.role}`);
    }
  },

  async testWechatLogin() {
    this.setData({ loginLoading: true });
    this.addTestResult('开始微信登录测试', '正在进行...');

    try {
      // 传入false，避免getUserProfile错误
      const result = await loginManager.wechatLogin(false);

      if (result.success) {
        this.addTestResult('微信登录', '成功');
        this.checkLoginStatus();
      } else if (result.needBind) {
        this.addTestResult('微信登录', '需要绑定账号');
        this.addTestResult('绑定数据', JSON.stringify(result.data));

        // 自动测试绑定功能
        this.setData({ lastBindData: result.data });
      } else {
        this.addTestResult('微信登录', '失败: ' + result.error);
      }
    } catch (error) {
      this.addTestResult('微信登录', '异常: ' + error.message);
      console.error('微信登录测试失败:', error);
    } finally {
      this.setData({ loginLoading: false });
    }
  },

  async testAccountBind() {
    const bindData = this.data.lastBindData;
    if (!bindData) {
      this.addTestResult('账号绑定测试', '请先进行微信登录测试');
      return;
    }

    this.addTestResult('账号绑定测试', '正在进行...');

    try {
      const result = await loginManager.bindAccount(bindData, 'test', 'password');

      if (result.success) {
        this.addTestResult('账号绑定', '成功');
        this.checkLoginStatus();
      } else {
        this.addTestResult('账号绑定', '失败: ' + result.error);
      }
    } catch (error) {
      this.addTestResult('账号绑定', '异常: ' + error.message);
      console.error('账号绑定测试失败:', error);
    }
  },

  async testTokenVerify() {
    this.addTestResult('Token验证测试', '正在进行...');
    
    try {
      const isValid = await loginManager.verifyToken();
      this.addTestResult('Token验证', isValid ? '有效' : '无效');
    } catch (error) {
      this.addTestResult('Token验证', '异常: ' + error.message);
    }
  },

  async testAPIConnection() {
    this.addTestResult('API连接测试', '正在进行...');

    try {
      // 测试一个简单的API调用
      const response = await loginManager.callAPI('test_connection', {});
      this.addTestResult('API连接', '成功: ' + JSON.stringify(response));
    } catch (error) {
      this.addTestResult('API连接', '失败: ' + error.message);
      console.error('API连接测试失败:', error);
    }
  },

  async testDatabaseCheck() {
    this.addTestResult('数据库检查', '正在进行...');

    try {
      const response = await loginManager.callAPI('check_database', {});
      if (response.status === 'success') {
        this.addTestResult('数据库检查', '数据库表完整');
      } else {
        this.addTestResult('数据库检查', '缺少表: ' + response.data.missing_tables.join(', '));
      }
    } catch (error) {
      this.addTestResult('数据库检查', '失败: ' + error.message);
      console.error('数据库检查失败:', error);
    }
  },

  async testDatabaseInit() {
    this.addTestResult('数据库初始化', '正在进行...');

    try {
      const response = await loginManager.callAPI('init_database', {});
      this.addTestResult('数据库初始化', response.message);
      if (response.data) {
        if (response.data.created && response.data.created.length > 0) {
          this.addTestResult('创建的表', response.data.created.join(', '));
        }
        if (response.data.errors && response.data.errors.length > 0) {
          this.addTestResult('创建失败', response.data.errors.join('; '));
        }
      }
    } catch (error) {
      this.addTestResult('数据库初始化', '失败: ' + error.message);
      console.error('数据库初始化失败:', error);
    }
  },

  testLogout() {
    loginManager.clearLoginData();
    this.checkLoginStatus();
    this.addTestResult('退出登录', '成功');
  },

  clearTestResults() {
    this.setData({
      testResults: []
    });
  },

  addTestResult(action, result) {
    const testResults = this.data.testResults;
    testResults.unshift({
      time: new Date().toLocaleTimeString(),
      action,
      result
    });
    this.setData({
      testResults: testResults.slice(0, 20) // 只保留最近20条
    });
  }
});
