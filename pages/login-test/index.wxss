page {
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

.container {
  padding: 32rpx;
  min-height: 100vh;
}

/* 状态显示区域 */
.status-section {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(25rpx);
  -webkit-backdrop-filter: blur(25rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 4rpx 16rpx rgba(71, 85, 105, 0.08);
}

.status-title {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(30, 41, 59, 0.9);
  margin-bottom: 24rpx;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.status-text {
  font-size: 28rpx;
  color: rgba(100, 116, 139, 0.8);
}

/* 测试按钮区域 */
.test-section {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(25rpx);
  -webkit-backdrop-filter: blur(25rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 4rpx 16rpx rgba(71, 85, 105, 0.08);
}

.test-title {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(30, 41, 59, 0.9);
  margin-bottom: 24rpx;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.test-btn {
  height: 80rpx;
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
  color: white;
  border-radius: 20rpx;
  border: none;
  font-size: 30rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.2);
  transition: all 0.2s ease;
}

.test-btn:active {
  transform: scale(0.98);
}

.test-btn.secondary {
  background: rgba(100, 116, 139, 0.1);
  color: rgba(100, 116, 139, 0.8);
  border: 1rpx solid rgba(100, 116, 139, 0.2);
  box-shadow: none;
}

/* 测试结果区域 */
.results-section {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(25rpx);
  -webkit-backdrop-filter: blur(25rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1rpx solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 4rpx 16rpx rgba(71, 85, 105, 0.08);
}

.results-title {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(30, 41, 59, 0.9);
  margin-bottom: 24rpx;
}

.results-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.result-item {
  display: flex;
  align-items: flex-start;
  padding: 16rpx 0;
  border-bottom: 1rpx solid rgba(226, 232, 240, 0.3);
}

.result-item:last-child {
  border-bottom: none;
}

.result-time {
  font-size: 24rpx;
  color: rgba(100, 116, 139, 0.6);
  width: 120rpx;
  flex-shrink: 0;
}

.result-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.result-action {
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(30, 41, 59, 0.9);
}

.result-text {
  font-size: 26rpx;
  color: rgba(100, 116, 139, 0.8);
}

.empty-results {
  text-align: center;
  padding: 60rpx 0;
  color: rgba(100, 116, 139, 0.6);
  font-size: 28rpx;
}
