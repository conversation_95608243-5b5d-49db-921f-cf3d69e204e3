const { loginManager } = require('../../utils/login.js');

Page({
  data: {
    userInfo: null,
    showChangePasswordModal: false,
    showChangeUsernameModal: false,
    passwordForm: {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    },
    usernameForm: {
      newUsername: ''
    },
    loading: false
  },

  onLoad: function (options) {
    this.loadUserInfo();
  },

  onShow: function() {
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo: function() {
    const userInfo = loginManager.getUserInfo();
    if (userInfo) {
      this.setData({
        userInfo: userInfo
      });
    } else {
      // 如果没有用户信息，返回上一页
      wx.navigateBack();
    }
  },

  // 修改密码
  onChangePassword: function() {
    this.setData({
      showChangePasswordModal: true,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    });
  },

  // 修改用户名
  onChangeUsername: function() {
    this.setData({
      showChangeUsernameModal: true,
      usernameForm: {
        newUsername: this.data.userInfo.username
      }
    });
  },

  // 解除微信绑定
  onUnbindWechat: function() {
    const self = this;
    wx.showModal({
      title: '确认解绑',
      content: '解绑后将无法使用微信快捷登录，确定要解绑吗？',
      success: async function(res) {
        if (res.confirm) {
          self.setData({ loading: true });
          
          try {
            const result = await loginManager.unbindAccount();
            if (result) {
              wx.showToast({
                title: '解绑成功',
                icon: 'success'
              });
              // 解绑成功后返回上一页
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            }
          } catch (error) {
            console.error('解绑失败:', error);
          } finally {
            self.setData({ loading: false });
          }
        }
      }
    });
  },

  // 查看登录记录
  onViewLoginHistory: function() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 关闭修改密码弹窗
  closePasswordModal: function() {
    this.setData({
      showChangePasswordModal: false
    });
  },

  // 关闭修改用户名弹窗
  closeUsernameModal: function() {
    this.setData({
      showChangeUsernameModal: false
    });
  },

  // 密码表单输入
  onOldPasswordInput: function(e) {
    this.setData({
      'passwordForm.oldPassword': e.detail.value
    });
  },

  onNewPasswordInput: function(e) {
    this.setData({
      'passwordForm.newPassword': e.detail.value
    });
  },

  onConfirmPasswordInput: function(e) {
    this.setData({
      'passwordForm.confirmPassword': e.detail.value
    });
  },

  // 用户名表单输入
  onUsernameInput: function(e) {
    this.setData({
      'usernameForm.newUsername': e.detail.value
    });
  },

  // 确认修改密码
  confirmChangePassword: async function() {
    const { oldPassword, newPassword, confirmPassword } = this.data.passwordForm;
    
    if (!oldPassword || !newPassword || !confirmPassword) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      wx.showToast({
        title: '两次密码不一致',
        icon: 'none'
      });
      return;
    }

    if (newPassword.length < 6) {
      wx.showToast({
        title: '密码至少6位',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    try {
      // 调用修改密码API
      const response = await loginManager.callAPI('change_password', {
        token: loginManager.token,
        old_password: oldPassword,
        new_password: newPassword
      });

      if (response.status === 'success') {
        wx.showToast({
          title: '密码修改成功',
          icon: 'success'
        });
        this.closePasswordModal();
      } else {
        throw new Error(response.message || '修改失败');
      }
    } catch (error) {
      wx.showToast({
        title: error.message || '修改失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 确认修改用户名
  confirmChangeUsername: async function() {
    const { newUsername } = this.data.usernameForm;
    
    if (!newUsername || newUsername.trim() === '') {
      wx.showToast({
        title: '请输入新用户名',
        icon: 'none'
      });
      return;
    }

    if (newUsername === this.data.userInfo.username) {
      wx.showToast({
        title: '用户名未改变',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    try {
      // 调用修改用户名API
      const response = await loginManager.callAPI('change_username', {
        token: loginManager.token,
        new_username: newUsername.trim()
      });

      if (response.status === 'success') {
        // 更新本地用户信息
        const updatedUserInfo = { ...this.data.userInfo, username: newUsername.trim() };
        this.setData({ userInfo: updatedUserInfo });
        wx.setStorageSync('user_info', updatedUserInfo);
        
        wx.showToast({
          title: '用户名修改成功',
          icon: 'success'
        });
        this.closeUsernameModal();
      } else {
        throw new Error(response.message || '修改失败');
      }
    } catch (error) {
      wx.showToast({
        title: error.message || '修改失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 阻止事件冒泡
  }
});
