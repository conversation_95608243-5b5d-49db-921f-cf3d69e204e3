Page({
  data: {
    showNotice: true, // 默认显示通知
    appVersion: '0.8.2', // 当前应用版本，请根据实际版本修改
    showDevTools: true // 显示开发测试工具，生产环境可设为false
  },
  
  // 关闭通知
  closeNotice() {
    this.setData({
      showNotice: false
    });
    
    // 保存关闭状态和时间到本地存储
    const currentTime = new Date().getTime();
    wx.setStorageSync('noticeInfo', {
      hideNotice: true,
      closeTime: currentTime,
      lastVersion: this.data.appVersion
    });
  },
  
  onLoad() {
    // 立即设置显示通知，避免延迟
    this.checkNoticeStatus();
  },

  onShow() {
    // 页面显示时再次检查，确保状态正确
    this.checkNoticeStatus();
  },
  
  // 检查通知显示状态
  checkNoticeStatus() {
    const noticeInfo = wx.getStorageSync('noticeInfo') || {};
    const currentTime = new Date().getTime();
    
    // 计算距离上次关闭的天数（默认7天后再次显示）
    const timeDiff = currentTime - (noticeInfo.closeTime || 0);
    const daysPassed = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    
    // 确定是否显示通知的条件:
    // 1. 从未关闭过
    // 2. 版本更新了
    // 3. 距离上次关闭已经过了7天
    const shouldShow = 
      !noticeInfo.hideNotice || 
      noticeInfo.lastVersion !== this.data.appVersion || 
      daysPassed >= 7;
    
    this.setData({
      showNotice: shouldShow
    });
  },

  navToDisclaimer() {
    // 免责声明文件在当前目录下
    wx.navigateTo({
      url: '/pages/index/disclaimer'
    });
  },

  // 显示更多功能开发中提示
  showComingSoon() {
    wx.showToast({
      title: '更多功能正在开发中',
      icon: 'none',
      duration: 2000
    });
  },

  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '平台常用查询工具',
      path: '/pages/index/index'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '平台常用查询工具',
      query: 'from=timeline'
    };
  },

  navigateTo(e) {
    const page = e.currentTarget.dataset.page;

    // TabBar页面需要使用switchTab
    const tabBarPages = ['oil-calculator', 'drug-concentration', 'seaSchedule'];
    if (tabBarPages.includes(page)) {
      wx.switchTab({
        url: `/pages/${page}/index`
      });
      return;
    }

    // 特殊处理管线尺寸对照表页面
    if (page === 'pipeSizeTable') {
      wx.navigateTo({
        url: '/pages/pipe-size-table/pipe-size-table'
      });
      return;
    }

    // 特殊处理PSI压力换算页面
    if (page === 'pressureConverter') {
      wx.navigateTo({
        url: '/pages/pressure-converter/pressure-converter'
      });
      return;
    }

    // 特殊处理振速位移换算页面
    if (page === 'vibrationConverter') {
      wx.navigateTo({
        url: '/pages/vibration-converter/vibration-converter'
      });
      return;
    }

    // 特殊处理工艺及仪表符号查询页面
    if (page === 'processSymbols') {
      wx.navigateTo({
        url: `/pages/${page}/${page}`
      });
      return;
    }

    // 特殊处理报警值设定标准查询页面
    if (page === 'alarmStandards') {
      wx.navigateTo({
        url: `/pages/${page}/${page}`
      });
      return;
    }

    // 特殊处理4-20mA模拟量转换页面
    if (page === 'analogConverter') {
      wx.navigateTo({
        url: `/pages/${page}/${page}`
      });
      return;
    }

    // 特殊处理设备保养时间计算页面
    if (page === 'equipmentMaintenance') {
      wx.navigateTo({
        url: `/pages/${page}/${page}`
      });
      return;
    }

    // 特殊处理一站津贴查询页面 - 直接跳转到登录页面
    if (page === 'stationJintie') {
      wx.navigateTo({
        url: `/pages/${page}/login`
      });
      return;
    }

    // 特殊处理防爆标识查询页面
    if (page === 'explosionProofQuery') {
      wx.navigateTo({
        url: `/pages/${page}/${page}`
      });
      return;
    }

    // 特殊处理高风险作业级别查询页面
    if (page === 'high-risk-operations') {
      wx.navigateTo({
        url: `/pages/${page}/${page}`
      });
      return;
    }

    // 特殊处理证件照换背景页面
    if (page === 'photoBackground') {
      wx.navigateTo({
        url: `/pages/${page}/${page}`
      });
      return;
    }

    if (page) {
      wx.navigateTo({
        url: `/pages/${page}/index`,
      });
    }
  }
});