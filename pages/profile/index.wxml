<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section" bindtap="onAvatarTap">
    <view class="user-avatar">
      <image src="{{userInfo.avatarUrl}}" class="avatar-image" mode="aspectFill"></image>
    </view>
    <view class="user-info">
      <text class="user-name">{{userInfo.nickName}}</text>
      <text class="user-status" wx:if="{{!isLoggedIn}}">点击登录</text>
      <text class="user-status" wx:else>{{userInfo.role || '已登录'}}</text>
    </view>
    <view class="arrow-icon">
      <text class="iconfont">›</text>
    </view>
  </view>

  <!-- 功能列表 -->
  <view class="function-list">
    <!-- 意见反馈 -->
    <view class="function-item" bindtap="onFeedback">
      <view class="function-icon">
        <text class="icon-emoji">💬</text>
      </view>
      <view class="function-info">
        <text class="function-title">意见反馈</text>
        <text class="function-desc">告诉我们您的建议</text>
      </view>
      <view class="arrow-icon">
        <text class="iconfont">›</text>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="function-item" wx:if="{{isLoggedIn}}" bindtap="onLogout">
      <view class="function-icon">
        <text class="icon-emoji">🚪</text>
      </view>
      <view class="function-info">
        <text class="function-title">退出登录</text>
        <text class="function-desc">退出当前账户</text>
      </view>
      <view class="arrow-icon">
        <text class="iconfont">›</text>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="bottom-info">
    <view class="company-info">
      <text class="company-name">海上平台常用计算工具</text>
      <text class="company-english">Henan Relations Co.,Ltd.</text>
    </view>
    <view class="contact-info">
      <text class="contact-item">E-mail：<EMAIL></text>
      <text class="contact-item">Http://www.sunxiyue.com</text>
    </view>
    <view class="welcome-text">
      <text>欢迎您使用本工具，如您发现本工具的任何缺陷可向开发组进行反馈，如您有更好的建议，也欢迎您提出，如您便用本工具时未能满足您的需求，或您有其他工具使用的需求时，也欢迎您向我们提出。开发组联系方式：</text>
      <text class="contact-email"><EMAIL></text>
    </view>
  </view>

  <!-- 登录弹窗 -->
  <view class="login-modal" wx:if="{{showLoginModal}}">
    <view class="modal-mask" bindtap="closeLoginModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">登录</text>
        <view class="modal-close" bindtap="closeLoginModal">
          <text>×</text>
        </view>
      </view>
      <view class="modal-body">
        <!-- 使用微信官方推荐的头像昵称填写方式 -->
        <view class="wechat-profile-section">
          <view class="profile-form">
            <view class="avatar-section">
              <text class="form-label">头像</text>
              <view class="avatar-display">
                <image class="avatar-image" src="{{userProfile.avatarUrl || '/images/zhuye/wd.png'}}" mode="aspectFill"></image>
                <view class="avatar-status">{{userProfile.avatarUrl ? '已选择' : '未选择'}}</view>
              </view>
            </view>

            <view class="nickname-section">
              <text class="form-label">昵称</text>
              <input
                class="nickname-input"
                type="nickname"
                placeholder="请输入昵称"
                value="{{userProfile.nickName}}"
                bind:input="onNicknameInput"
                maxlength="20"
              />
            </view>
          </view>

          <!-- 合并的头像选择和登录按钮 -->
          <button
            class="login-btn {{!userProfile.avatarUrl ? 'avatar-select-btn' : ''}}"
            open-type="{{!userProfile.avatarUrl ? 'chooseAvatar' : ''}}"
            bind:chooseavatar="onChooseAvatarAndLogin"
            bindtap="onWechatLoginWithProfile"
            loading="{{loginLoading}}"
          >
            <text class="login-btn-text">
              {{!userProfile.avatarUrl ? '选择头像并登录' : '微信登录'}}
            </text>
          </button>
        </view>

        <view class="login-tip">
          <text>{{!userProfile.avatarUrl ? '点击按钮选择头像并登录' : '请输入昵称后登录'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 账号绑定弹窗 -->
  <view class="bind-modal" wx:if="{{showBindModal}}">
    <view class="modal-mask" bindtap="closeBindModal"></view>
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">绑定系统账号</text>
        <view class="modal-close" bindtap="closeBindModal">
          <text>×</text>
        </view>
      </view>
      <view class="modal-body">
        <view class="bind-info">
          <text class="bind-text">您的微信账号尚未绑定系统账号</text>
          <text class="bind-desc">绑定后可使用完整功能，不绑定将有功能限制</text>
        </view>
        <view class="form-group">
          <text class="form-label">用户名</text>
          <input class="form-input" placeholder="请输入用户名" value="{{bindForm.username}}" bindinput="onBindUsernameInput" />
        </view>
        <view class="form-group">
          <text class="form-label">密码</text>
          <input class="form-input" type="password" placeholder="请输入密码" value="{{bindForm.password}}" bindinput="onBindPasswordInput" />
        </view>
        <view class="skip-bind-info">
          <text class="skip-text">不绑定账号的功能限制：</text>
          <text class="limit-item">• 无法使用仪表位号及阀门编号查询功能</text>
          <text class="limit-item">• 无法使用一站津贴查询功能</text>
          <text class="limit-item">• 无法同步个人设置</text>
        </view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn skip" bindtap="skipBind">跳过绑定</button>
        <button class="modal-btn confirm" bindtap="confirmBind" loading="{{bindLoading}}">立即绑定</button>
      </view>
    </view>
  </view>
</view>
