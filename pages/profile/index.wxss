page {
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  -webkit-tap-highlight-color: transparent;
}

.container {
  padding: 0;
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg,
    rgba(248, 250, 252, 0.95) 0%,
    rgba(241, 245, 249, 0.9) 50%,
    rgba(226, 232, 240, 0.95) 100%);
  position: relative;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
  z-index: 0;
}

/* 用户信息区域 */
.user-section {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(25rpx);
  -webkit-backdrop-filter: blur(25rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 28rpx;
  margin: 32rpx;
  padding: 40rpx 32rpx;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
  box-shadow:
    0 8rpx 24rpx rgba(71, 85, 105, 0.08),
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9),
    inset 0 -1rpx 0 rgba(226, 232, 240, 0.3);
  transition: all 0.2s ease;
}

.user-section:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.8);
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  margin-right: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    0 4rpx 16rpx rgba(71, 85, 105, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: rgba(30, 41, 59, 0.9);
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.user-status {
  font-size: 28rpx;
  color: rgba(100, 116, 139, 0.8);
}

.arrow-icon {
  font-size: 40rpx;
  color: rgba(100, 116, 139, 0.6);
  font-weight: 300;
}

/* 功能列表 */
.function-list {
  margin: 0 32rpx 32rpx 32rpx;
  position: relative;
  z-index: 1;
}

.function-item {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(25rpx);
  -webkit-backdrop-filter: blur(25rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 24rpx;
  margin-bottom: 16rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  box-shadow:
    0 4rpx 16rpx rgba(71, 85, 105, 0.06),
    0 1rpx 4rpx rgba(71, 85, 105, 0.03),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9),
    inset 0 -1rpx 0 rgba(226, 232, 240, 0.3);
  transition: all 0.2s ease;
}

.function-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.8);
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15rpx);
  -webkit-backdrop-filter: blur(15rpx);
  box-shadow:
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
}

.icon-emoji {
  font-size: 36rpx;
  line-height: 1;
}

.function-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.function-title {
  font-size: 32rpx;
  font-weight: 500;
  color: rgba(30, 41, 59, 0.9);
  margin-bottom: 4rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.function-desc {
  font-size: 26rpx;
  color: rgba(100, 116, 139, 0.8);
}

/* 底部信息 */
.bottom-info {
  padding: 48rpx 32rpx;
  position: relative;
  z-index: 1;
  margin-top: auto;
}

.company-info {
  text-align: center;
  margin-bottom: 32rpx;
  padding: 32rpx;
  background: rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(15rpx);
  -webkit-backdrop-filter: blur(15rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(59, 130, 246, 0.2);
}

.company-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(30, 41, 59, 0.9);
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.company-english {
  display: block;
  font-size: 24rpx;
  color: rgba(100, 116, 139, 0.8);
}

.contact-info {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid rgba(226, 232, 240, 0.5);
  box-shadow:
    0 4rpx 16rpx rgba(71, 85, 105, 0.06),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.contact-item {
  display: block;
  font-size: 24rpx;
  color: rgba(100, 116, 139, 0.8);
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.welcome-text {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 32rpx;
  border: 1rpx solid rgba(226, 232, 240, 0.5);
  box-shadow:
    0 4rpx 16rpx rgba(71, 85, 105, 0.06),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.welcome-text text {
  display: block;
  font-size: 24rpx;
  color: rgba(100, 116, 139, 0.8);
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.contact-email {
  color: #2980b9 !important;
  font-weight: 500;
  margin-bottom: 0 !important;
}

/* 登录弹窗 */
.login-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

.modal-content {
  width: 600rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(30rpx);
  -webkit-backdrop-filter: blur(30rpx);
  border-radius: 32rpx;
  position: relative;
  z-index: 1001;
  box-shadow:
    0 16rpx 48rpx rgba(71, 85, 105, 0.15),
    0 4rpx 16rpx rgba(71, 85, 105, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(226, 232, 240, 0.6);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx 0 40rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: rgba(30, 41, 59, 0.9);
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: rgba(100, 116, 139, 0.6);
  border-radius: 30rpx;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
}

.modal-close:active {
  background: rgba(0, 0, 0, 0.05);
}

.modal-body {
  padding: 40rpx;
  text-align: center;
}

.login-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  margin: 0 auto 32rpx auto;
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    0 4rpx 16rpx rgba(71, 85, 105, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.login-avatar-image {
  width: 100%;
  height: 100%;
}

.login-desc {
  font-size: 28rpx;
  color: rgba(100, 116, 139, 0.8);
  margin-bottom: 48rpx;
  display: block;
}

/* 登录按钮 - 重新设计，覆盖全局样式 */
.login-btn {
  width: 100% !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%) !important;
  border-radius: 22rpx !important;
  border: none !important;
  color: white !important;
  font-size: 32rpx !important;
  font-weight: 500 !important;
  text-align: center !important;
  padding: 0 !important;
  margin: 0 !important;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3) !important;
  transition: all 0.2s ease !important;
}

.login-btn::after {
  border: none !important;
}

.login-btn:active {
  transform: scale(0.98) !important;
  opacity: 0.9 !important;
}

/* 头像选择按钮样式 */
.login-btn.avatar-select-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.3) !important;
}

/* 隐藏按钮内的文字元素，直接在按钮上显示文字 */
.login-btn-text {
  display: none;
}

.login-tip {
  margin-top: 16rpx;
  text-align: center;
}

.login-tip text {
  font-size: 24rpx;
  color: rgba(100, 116, 139, 0.7);
  line-height: 1.4;
}

/* 微信用户资料填写区域 */
.wechat-profile-section {
  width: 100%;
}

.profile-form {
  margin-bottom: 32rpx;
}

.avatar-section, .nickname-section {
  margin-bottom: 24rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(30, 41, 59, 0.9);
  margin-bottom: 12rpx;
}

.avatar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  background: transparent;
  border: none;
  width: 120rpx;
  height: 120rpx;
  position: relative;
}

.avatar-wrapper::after {
  border: none;
}

.avatar-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(226, 232, 240, 0.6);
}

.avatar-tip {
  position: absolute;
  bottom: -24rpx;
  font-size: 20rpx;
  color: rgba(100, 116, 139, 0.7);
  white-space: nowrap;
}

.nickname-input {
  width: 100%;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15rpx);
  -webkit-backdrop-filter: blur(15rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.5);
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: rgba(30, 41, 59, 0.9);
  box-sizing: border-box;
  box-shadow:
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
  transition: all 0.2s ease;
}

.nickname-input:focus {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow:
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    0 0 0 4rpx rgba(59, 130, 246, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
}

.login-tip {
  margin-top: 16rpx;
  text-align: center;
}

.login-tip text {
  font-size: 24rpx;
  color: rgba(100, 116, 139, 0.7);
  line-height: 1.4;
}

/* 账号绑定弹窗 */
.bind-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bind-info {
  text-align: center;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 16rpx;
  border: 1rpx solid rgba(59, 130, 246, 0.2);
}

.bind-text {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: rgba(30, 41, 59, 0.9);
  margin-bottom: 8rpx;
}

.bind-desc {
  display: block;
  font-size: 26rpx;
  color: rgba(100, 116, 139, 0.8);
}

.skip-bind-info {
  margin-top: 24rpx;
  padding: 20rpx;
  background: rgba(251, 146, 60, 0.1);
  border-radius: 12rpx;
  border: 1rpx solid rgba(251, 146, 60, 0.2);
}

.skip-text {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: rgba(194, 65, 12, 0.9);
  margin-bottom: 12rpx;
}

.limit-item {
  display: block;
  font-size: 24rpx;
  color: rgba(154, 52, 18, 0.8);
  margin-bottom: 6rpx;
  line-height: 1.4;
}

.form-group {
  margin-bottom: 24rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(30, 41, 59, 0.9);
  margin-bottom: 12rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15rpx);
  -webkit-backdrop-filter: blur(15rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.5);
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: rgba(30, 41, 59, 0.9);
  box-sizing: border-box;
  box-shadow:
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
  transition: all 0.2s ease;
}

.form-input:focus {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow:
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    0 0 0 4rpx rgba(59, 130, 246, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  margin-top: 32rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.modal-btn.cancel {
  background: rgba(100, 116, 139, 0.1);
  color: rgba(100, 116, 139, 0.8);
  border: 1rpx solid rgba(100, 116, 139, 0.2);
}

.modal-btn.cancel:active {
  background: rgba(100, 116, 139, 0.15);
  transform: scale(0.98);
}

.modal-btn.skip {
  background: rgba(251, 146, 60, 0.1);
  color: rgba(194, 65, 12, 0.8);
  border: 1rpx solid rgba(251, 146, 60, 0.3);
}

.modal-btn.skip:active {
  background: rgba(251, 146, 60, 0.15);
  transform: scale(0.98);
}

.modal-btn.confirm {
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  box-shadow:
    0 4rpx 16rpx rgba(0, 122, 255, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.modal-btn.confirm:active {
  transform: scale(0.98);
  box-shadow:
    0 2rpx 8rpx rgba(0, 122, 255, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}
