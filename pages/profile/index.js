const { loginManager } = require('../../utils/login.js');

Page({
  data: {
    userInfo: {
      avatarUrl: '/images/zhuye/wd.png', // 默认头像
      nickName: '匿名用户'
    },
    isLoggedIn: false,
    showLoginModal: false,
    showBindModal: false,
    loginLoading: false,
    bindLoading: false,
    bindData: null,
    bindForm: {
      username: '',
      password: ''
    },
    isGuestMode: false, // 游客模式标识
    userProfile: {
      avatarUrl: '',
      nickName: ''
    }
  },

  onLoad: function (options) {
    // 检查是否已登录
    this.checkLoginStatus();
  },

  onShow: function() {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus();
  },

  // 检查登录状态
  async checkLoginStatus() {
    const isLoggedIn = loginManager.isLoggedIn();
    const isGuestMode = wx.getStorageSync('guest_mode') || false;

    if (isLoggedIn) {
      // 验证token有效性
      const isValid = await loginManager.verifyToken();
      if (isValid) {
        const userInfo = loginManager.getUserInfo();
        this.setData({
          isLoggedIn: true,
          isGuestMode: false,
          userInfo: {
            avatarUrl: userInfo.avatar || '/images/zhuye/wd.png',
            nickName: userInfo.username || '系统用户',
            role: this.formatUserRole(userInfo)
          }
        });
      } else {
        this.setData({
          isLoggedIn: false,
          isGuestMode: false,
          userInfo: {
            avatarUrl: '/images/zhuye/wd.png',
            nickName: '匿名用户'
          }
        });
      }
    } else if (isGuestMode) {
      // 游客模式
      this.setData({
        isLoggedIn: true,
        isGuestMode: true,
        userInfo: {
          avatarUrl: '/images/zhuye/wd.png',
          nickName: '游客用户',
          role: '游客模式 (功能受限)'
        }
      });
    } else {
      this.setData({
        isLoggedIn: false,
        isGuestMode: false,
        userInfo: {
          avatarUrl: '/images/zhuye/wd.png',
          nickName: '匿名用户'
        }
      });
    }
  },

  // 格式化用户角色显示
  formatUserRole(userInfo) {
    if (!userInfo) return '';

    let roleText = '普通用户';
    if (userInfo.role === 'admin') {
      roleText = '管理员';
    } else if (userInfo.role === 'manager') {
      roleText = '普通管理员';
    }

    if (userInfo.is_station_staff) {
      roleText += ' | 一站人员';
    }

    return roleText;
  },

  // 点击头像区域
  onAvatarTap: function() {
    if (!this.data.isLoggedIn) {
      this.setData({
        showLoginModal: true
      });
    } else if (this.data.isGuestMode) {
      // 游客模式，提示绑定账号
      wx.showModal({
        title: '游客模式',
        content: '您当前处于游客模式，绑定系统账号后可使用完整功能。是否立即绑定？',
        confirmText: '立即绑定',
        cancelText: '继续游客',
        success: (res) => {
          if (res.confirm) {
            const openid = wx.getStorageSync('wechat_openid');
            this.setData({
              showBindModal: true,
              bindData: {
                openid: openid,
                wechat_user_id: '1' // 游客模式下的临时ID
              }
            });
          }
        }
      });
    } else {
      // 已绑定用户进入个人管理页面
      wx.navigateTo({
        url: '/pages/account-manage/index'
      });
    }
  },

  // 关闭登录弹窗
  closeLoginModal: function() {
    this.setData({
      showLoginModal: false
    });
  },

  // 关闭绑定弹窗
  closeBindModal: function() {
    this.setData({
      showBindModal: false,
      bindForm: {
        username: '',
        password: ''
      }
    });
  },

  // 选择头像
  onChooseAvatar(e) {
    console.log('选择头像:', e.detail);
    const { avatarUrl } = e.detail;
    this.setData({
      'userProfile.avatarUrl': avatarUrl
    });
  },

  // 输入昵称
  onNicknameInput(e) {
    console.log('输入昵称:', e.detail.value);
    this.setData({
      'userProfile.nickName': e.detail.value
    });
  },

  // 使用用户资料进行微信登录
  async onWechatLoginWithProfile() {
    const { avatarUrl, nickName } = this.data.userProfile;

    if (!nickName || nickName.trim() === '') {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }

    this.setData({ loginLoading: true });

    try {
      // 构造用户信息对象
      const userInfo = {
        avatarUrl: avatarUrl || '/images/zhuye/wd.png',
        nickName: nickName.trim(),
        gender: 0, // 默认值
        city: '',
        province: '',
        country: ''
      };

      console.log('使用用户资料登录:', userInfo);

      // 进行微信登录
      const result = await loginManager.wechatLoginWithUserInfo(userInfo);
      this.handleLoginResult(result);

    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loginLoading: false });
    }
  },

  // 处理用户信息授权（保留兼容性）
  async onGetUserInfo(e) {
    console.log('用户信息授权结果:', e.detail);

    this.setData({ loginLoading: true });

    try {
      let userInfo = null;

      // 检查用户是否授权
      if (e.detail.userInfo) {
        userInfo = e.detail.userInfo;
        console.log('获取到用户信息:', userInfo);
      } else {
        console.log('用户拒绝授权或获取用户信息失败');
      }

      // 进行微信登录，传入获取到的用户信息
      let result = await loginManager.wechatLoginWithUserInfo(userInfo);

      if (result.success) {
        // 登录成功
        const userInfo = result.data.user;
        if (userInfo) {
          this.setData({
            isLoggedIn: true,
            userInfo: {
              avatarUrl: userInfo.avatar || '/images/zhuye/wd.png',
              nickName: userInfo.username || '系统用户',
              role: this.formatUserRole(userInfo)
            },
            showLoginModal: false
          });
        } else {
          console.error('登录成功但用户信息为空:', result);
          wx.showToast({
            title: '登录数据异常',
            icon: 'none'
          });
        }
      } else if (result.needBind) {
        // 需要绑定账号
        this.setData({
          showLoginModal: false,
          showBindModal: true,
          bindData: result.data
        });
      }
    } catch (error) {
      console.error('微信登录失败:', error);
    } finally {
      this.setData({ loginLoading: false });
    }
  },

  // 现代微信登录（使用头像昵称组件）
  async onModernWechatLogin() {
    this.setData({ loginLoading: true });

    try {
      // 先获取微信登录code
      const loginRes = await this.wxLogin();
      console.log('微信登录结果:', loginRes);

      // 显示头像昵称选择器
      const self = this;
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: async (res) => {
          console.log('获取用户信息成功:', res.userInfo);

          try {
            // 使用获取到的用户信息进行登录
            const result = await loginManager.wechatLoginWithUserInfo(res.userInfo);
            self.handleLoginResult(result);
          } catch (error) {
            console.error('登录失败:', error);
            wx.showToast({
              title: '登录失败',
              icon: 'none'
            });
          } finally {
            self.setData({ loginLoading: false });
          }
        },
        fail: async (error) => {
          console.log('用户拒绝授权:', error);

          // 即使用户拒绝授权，也可以继续登录流程（不获取用户信息）
          try {
            const result = await loginManager.wechatLoginWithUserInfo(null);
            self.handleLoginResult(result);
          } catch (loginError) {
            console.error('登录失败:', loginError);
            wx.showToast({
              title: '登录失败',
              icon: 'none'
            });
          } finally {
            self.setData({ loginLoading: false });
          }
        }
      });

    } catch (error) {
      console.error('微信登录失败:', error);
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      });
      this.setData({ loginLoading: false });
    }
  },

  // 微信登录辅助方法
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  },

  // 处理登录结果
  handleLoginResult(result) {
    if (result.success) {
      // 登录成功
      const userInfo = result.data.user;
      if (userInfo) {
        this.setData({
          isLoggedIn: true,
          isGuestMode: false,
          userInfo: {
            avatarUrl: userInfo.avatar || '/images/zhuye/wd.png',
            nickName: userInfo.username || '系统用户',
            role: this.formatUserRole(userInfo)
          },
          showLoginModal: false
        });
      }
    } else if (result.needBind) {
      // 需要绑定账号
      this.setData({
        showLoginModal: false,
        showBindModal: true,
        bindData: result.data
      });
    } else {
      wx.showToast({
        title: result.error || '登录失败',
        icon: 'none'
      });
    }
  },

  // 确认绑定账号
  async confirmBind() {
    const { username, password } = this.data.bindForm;
    const { bindData } = this.data;

    if (!username || !password) {
      wx.showToast({
        title: '请输入用户名和密码',
        icon: 'none'
      });
      return;
    }

    this.setData({ bindLoading: true });

    try {
      const result = await loginManager.bindAccount(bindData, username, password);

      if (result.success) {
        // 绑定成功，从游客模式升级为正式用户
        const userInfo = result.data.user;

        // 清除游客模式标识
        wx.removeStorageSync('guest_mode');
        wx.removeStorageSync('wechat_openid');

        this.setData({
          isLoggedIn: true,
          isGuestMode: false,
          userInfo: {
            avatarUrl: userInfo.avatar || '/images/zhuye/wd.png',
            nickName: userInfo.username || '系统用户',
            role: this.formatUserRole(userInfo)
          },
          showBindModal: false,
          bindForm: { username: '', password: '' }
        });

        wx.showToast({
          title: '绑定成功，已升级为正式用户',
          icon: 'success',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('绑定失败:', error);
    } finally {
      this.setData({ bindLoading: false });
    }
  },

  // 跳过绑定，进入游客模式
  skipBind: function() {
    const self = this;
    wx.showModal({
      title: '确认跳过绑定',
      content: '跳过绑定后将以游客身份使用，部分功能将受限。您可以随时在个人中心进行绑定。',
      confirmText: '确认跳过',
      cancelText: '继续绑定',
      success: function(res) {
        if (res.confirm) {
          // 进入游客模式
          self.setData({
            isLoggedIn: true,
            isGuestMode: true,
            userInfo: {
              avatarUrl: '/images/zhuye/wd.png',
              nickName: '游客用户',
              role: '游客模式 (功能受限)'
            },
            showBindModal: false
          });

          // 保存游客模式标识和微信信息
          wx.setStorageSync('guest_mode', true);
          wx.setStorageSync('wechat_openid', self.data.bindData.openid);

          wx.showToast({
            title: '已进入游客模式',
            icon: 'success'
          });
        }
      }
    });
  },

  // 退出登录
  onLogout: function() {
    const self = this;
    const logoutText = this.data.isGuestMode ? '退出游客模式' : '退出登录';
    const contentText = this.data.isGuestMode ? '确定要退出游客模式吗？' : '确定要退出登录吗？';

    wx.showModal({
      title: '提示',
      content: contentText,
      success: async function(res) {
        if (res.confirm) {
          if (self.data.isGuestMode) {
            // 退出游客模式
            wx.removeStorageSync('guest_mode');
            wx.removeStorageSync('wechat_openid');
          } else {
            // 使用新的正式退出登录功能
            await loginManager.logout();
          }

          self.setData({
            userInfo: {
              avatarUrl: '/images/zhuye/wd.png',
              nickName: '匿名用户'
            },
            isLoggedIn: false,
            isGuestMode: false
          });

          wx.showToast({
            title: logoutText + '成功',
            icon: 'success'
          });
        }
      }
    });
  },

  // 意见反馈
  onFeedback: function() {
    wx.navigateTo({
      url: '/pages/feedback/index'
    });
  },

  // 表单输入处理
  onBindUsernameInput: function(e) {
    this.setData({
      'bindForm.username': e.detail.value
    });
  },

  onBindPasswordInput: function(e) {
    this.setData({
      'bindForm.password': e.detail.value
    });
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 阻止事件冒泡
  }
});
