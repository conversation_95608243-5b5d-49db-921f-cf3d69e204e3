# 微信登录功能集成说明

## 已完成的功能

1. **微信登录工具类** (`utils/login.js`)
   - 基于shili示例代码集成
   - 支持微信授权登录
   - 支持账号绑定功能
   - 支持Token验证和管理
   - 支持账号解绑功能

2. **我的页面集成** (`pages/profile/index.*`)
   - 集成了完整的微信登录流程
   - 支持登录状态检查和显示
   - 支持账号绑定弹窗
   - 支持退出登录功能

## 配置要求

### 1. 微信小程序配置

在微信小程序管理后台需要配置：

**服务器域名配置**：
- request合法域名：`https://sunxiyue.com`

**在 `project.config.json` 中确保有以下配置**：
```json
{
  "setting": {
    "urlCheck": false
  }
}
```

### 2. 后端API配置

确保后端API地址正确：
- API地址：`https://sunxiyue.com/zdh/api/wechat_login_api.php`
- 需要配置微信小程序的AppID和Secret

### 3. 数据库配置

需要确保后端数据库包含以下表：
- `wechat_users` - 微信用户信息
- `user_wechat_bindings` - 用户绑定关系
- `wechat_tokens` - 登录Token
- `wechat_login_logs` - 登录日志
- `users` - 系统用户表

## 使用流程

### 首次登录用户
1. 点击头像区域 → 弹出登录弹窗
2. 点击"微信登录" → 微信授权
3. 系统检测未绑定 → 弹出绑定弹窗
4. 输入系统账号密码 → 完成绑定
5. 登录成功，显示用户信息

### 已绑定用户
1. 点击头像区域 → 弹出登录弹窗
2. 点击"微信登录" → 微信授权
3. 系统识别已绑定账号 → 直接登录成功

### 退出登录
1. 点击"退出登录"功能项
2. 确认退出 → 清除本地登录状态

## 功能特点

1. **安全性**
   - 使用Token机制管理登录状态
   - Token有7天有效期
   - 支持Token验证和自动续期

2. **用户体验**
   - Apple风格的UI设计
   - 流畅的登录流程
   - 清晰的状态提示

3. **兼容性**
   - 兼容现有用户系统
   - 支持角色权限显示
   - 支持一站人员标识

## 测试建议

1. **开发环境测试**
   - 在微信开发者工具中测试登录流程
   - 测试网络请求是否正常
   - 测试Token存储和验证

2. **真机测试**
   - 在真实微信环境中测试
   - 测试微信授权流程
   - 测试账号绑定功能

## 注意事项

1. **网络配置**
   - 确保服务器域名已在微信后台配置
   - 确保API接口可以正常访问

2. **权限配置**
   - 确保小程序有获取用户信息的权限
   - 确保后端API有正确的CORS配置

3. **错误处理**
   - 已集成完整的错误处理机制
   - 网络错误会有相应提示
   - 登录失败会显示具体原因

## 测试页面

已创建专门的测试页面 `pages/login-test/index.*`：
- 可以测试微信登录流程
- 可以测试Token验证功能
- 可以测试退出登录功能
- 实时显示测试结果和状态

**访问方式**：
- 在主页可以看到"开发测试工具"区域
- 点击"微信登录测试"进入测试页面

## 文件清单

### 新增文件
- `utils/login.js` - 微信登录工具类
- `pages/profile/index.*` - 我的页面（已更新）
- `pages/feedback/index.*` - 意见反馈页面
- `pages/login-test/index.*` - 登录测试页面
- `README_WECHAT_LOGIN.md` - 本说明文档

### 修改文件
- `app.json` - 更新页面路径和tabBar配置
- `project.config.json` - 添加urlCheck配置
- `pages/index/index.*` - 添加测试工具入口

## 部署检查清单

### 开发环境
- [x] 创建微信登录工具类
- [x] 集成到我的页面
- [x] 创建测试页面
- [x] 配置网络请求权限

### 生产环境部署前
- [ ] 确认微信小程序AppID和Secret配置
- [ ] 确认服务器域名白名单配置
- [ ] 确认数据库表结构完整
- [ ] 测试完整登录流程
- [ ] 关闭开发测试工具显示（设置showDevTools为false）

## 后续优化

1. 可以添加更多的用户信息展示
2. 可以添加账号管理功能
3. 可以添加登录历史查看
4. 可以优化UI交互效果
5. 可以添加自动登录功能
6. 可以添加多账号切换功能
