/**
 * 微信小程序登录功能示例代码
 * 请将此代码集成到您的微信小程序中
 */

// 配置API基础URL
const API_BASE_URL = 'https://zdh.sunxiyue.com/api/wechat_login_api.php';

/**
 * 微信登录管理类
 */
class WechatLoginManager {
  constructor() {
    this.token = wx.getStorageSync('wechat_token') || '';
    this.userInfo = wx.getStorageSync('user_info') || null;
  }

  /**
   * 微信登录
   */
  async wechatLogin() {
    try {
      // 1. 获取微信授权码
      const loginRes = await this.wxLogin();
      console.log('微信登录结果:', loginRes);

      // 2. 获取用户信息（可选）
      let userInfo = null;
      try {
        userInfo = await this.getUserProfile();
      } catch (e) {
        console.log('用户取消授权或获取用户信息失败:', e);
      }

      // 3. 调用后端登录接口
      const response = await this.callAPI('wechat_login', {
        code: loginRes.code,
        userInfo: userInfo
      });

      if (response.status === 'success') {
        // 登录成功，保存token和用户信息
        this.token = response.data.token;
        this.userInfo = response.data.user;
        
        wx.setStorageSync('wechat_token', this.token);
        wx.setStorageSync('user_info', this.userInfo);
        wx.setStorageSync('token_expires_at', response.data.expires_at);

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });

        return {
          success: true,
          data: response.data
        };

      } else if (response.status === 'need_bind') {
        // 需要绑定账号
        return {
          success: false,
          needBind: true,
          data: response.data
        };

      } else {
        throw new Error(response.message || '登录失败');
      }

    } catch (error) {
      console.error('微信登录失败:', error);
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      });
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 绑定系统账号
   */
  async bindAccount(bindData, username, password) {
    try {
      const response = await this.callAPI('bind_account', {
        openid: bindData.openid,
        wechat_user_id: bindData.wechat_user_id,
        username: username,
        password: password
      });

      if (response.status === 'success') {
        // 绑定成功，保存token和用户信息
        this.token = response.data.token;
        this.userInfo = response.data.user;
        
        wx.setStorageSync('wechat_token', this.token);
        wx.setStorageSync('user_info', this.userInfo);
        wx.setStorageSync('token_expires_at', response.data.expires_at);

        wx.showToast({
          title: '绑定成功',
          icon: 'success'
        });

        return {
          success: true,
          data: response.data
        };
      } else {
        throw new Error(response.message || '绑定失败');
      }

    } catch (error) {
      console.error('账号绑定失败:', error);
      wx.showToast({
        title: error.message || '绑定失败',
        icon: 'none'
      });
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 验证Token有效性
   */
  async verifyToken() {
    if (!this.token) {
      return false;
    }

    try {
      const response = await this.callAPI('verify_token', {
        token: this.token
      });

      if (response.status === 'success') {
        // 更新用户信息
        this.userInfo = response.data.user;
        wx.setStorageSync('user_info', this.userInfo);
        return true;
      } else {
        // Token无效，清除本地存储
        this.clearLoginData();
        return false;
      }

    } catch (error) {
      console.error('Token验证失败:', error);
      this.clearLoginData();
      return false;
    }
  }

  /**
   * 解绑账号
   */
  async unbindAccount() {
    try {
      const response = await this.callAPI('unbind_account', {
        token: this.token
      });

      if (response.status === 'success') {
        this.clearLoginData();
        wx.showToast({
          title: '解绑成功',
          icon: 'success'
        });
        return true;
      } else {
        throw new Error(response.message || '解绑失败');
      }

    } catch (error) {
      console.error('解绑失败:', error);
      wx.showToast({
        title: error.message || '解绑失败',
        icon: 'none'
      });
      return false;
    }
  }

  /**
   * 检查登录状态
   */
  isLoggedIn() {
    return !!(this.token && this.userInfo);
  }

  /**
   * 获取用户信息
   */
  getUserInfo() {
    return this.userInfo;
  }

  /**
   * 清除登录数据
   */
  clearLoginData() {
    this.token = '';
    this.userInfo = null;
    wx.removeStorageSync('wechat_token');
    wx.removeStorageSync('user_info');
    wx.removeStorageSync('token_expires_at');
  }

  /**
   * 微信登录
   */
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  }

  /**
   * 获取用户信息
   */
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          resolve(res.userInfo);
        },
        fail: reject
      });
    });
  }

  /**
   * 调用API接口
   */
  async callAPI(action, data = {}) {
    return new Promise((resolve, reject) => {
      const requestData = {
        action: action,
        ...data
      };

      wx.request({
        url: API_BASE_URL,
        method: 'POST',
        data: requestData,
        header: {
          'Content-Type': 'application/json',
          ...(this.token ? { 'Authorization': `Bearer ${this.token}` } : {})
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.data.message || '请求失败'}`));
          }
        },
        fail: (error) => {
          reject(new Error('网络请求失败'));
        }
      });
    });
  }
}

// 导出登录管理器实例
const loginManager = new WechatLoginManager();

module.exports = {
  loginManager,
  WechatLoginManager
};
