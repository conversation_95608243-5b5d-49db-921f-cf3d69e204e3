# 微信登录问题排查指南

## 当前问题分析

根据你提供的错误日志，主要有以下问题：

### 1. ✅ 微信登录code获取成功
```
微信登录结果: {errMsg: "login:ok", code: "0c1hmmml290E3g4YK3nl2Mjpej2hmmmZ"}
```
这说明微信登录的第一步是成功的。

### 2. ❌ getUserProfile调用失败
```
getUserProfile:fail can only be invoked by user TAP gesture.
```
**原因**: `getUserProfile` 必须在用户点击事件中直接调用，不能在异步函数中调用。

**解决方案**: 已修改代码，现在 `getUserProfile` 只在用户主动点击登录按钮时调用。

### 3. ❌ API请求返回400错误
```
POST https://sunxiyue.com/zdh/api/wechat_login_api.php 400
```
**可能原因**:
- 后端API配置问题
- 微信小程序AppID/Secret配置错误
- 请求参数格式问题
- 数据库连接问题

## 修复内容

### 1. 修复getUserProfile调用方式
- 添加了参数控制是否获取用户信息
- 只在用户主动点击时获取用户信息
- 即使获取用户信息失败，登录流程仍可继续

### 2. 增强调试功能
- 添加了详细的API请求和响应日志
- 添加了调试模式开关
- 添加了API连接测试功能

### 3. 改进错误处理
- 更详细的错误信息
- 更好的错误分类和处理

## 测试步骤

### 1. 基础连接测试
1. 打开"微信登录测试"页面
2. 点击"API连接测试"按钮
3. 查看控制台输出和测试结果

### 2. 微信登录测试
1. 点击"微信登录测试"按钮
2. 查看控制台的详细日志：
   - API请求URL
   - API请求数据
   - API响应状态码
   - API响应数据

### 3. 检查后端配置

确认后端API配置：

#### 检查微信小程序配置
在后端 `includes/config.php` 中确认：
```php
// 微信小程序配置
define('WECHAT_MINI_APPID', 'wx6aa87efc3d1e3605');     // 你的实际AppID
define('WECHAT_MINI_SECRET', 'your_actual_secret_here'); // 你的实际Secret
```

#### 检查数据库连接
确认数据库连接正常，包含以下表：
- `wechat_users`
- `user_wechat_bindings` 
- `wechat_tokens`
- `wechat_login_logs`
- `users`

## 常见问题解决

### 问题1: API返回400错误
**检查步骤**:
1. 确认API URL是否正确访问
2. 检查微信小程序AppID和Secret配置
3. 检查数据库连接
4. 查看服务器错误日志

**临时测试方法**:
```javascript
// 在浏览器中直接访问API测试
fetch('https://sunxiyue.com/zdh/api/wechat_login_api.php', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    action: 'test_connection'
  })
})
.then(res => res.json())
.then(data => console.log(data));
```

### 问题2: 微信AppID配置错误
**检查方法**:
1. 登录微信公众平台
2. 进入小程序管理后台
3. 查看"开发" -> "开发设置"
4. 确认AppID是否为 `wx6aa87efc3d1e3605`

### 问题3: 网络域名配置
**配置步骤**:
1. 在微信公众平台小程序管理后台
2. 进入"开发" -> "开发设置"
3. 在"服务器域名"中添加：
   - request合法域名: `https://sunxiyue.com`

## 调试技巧

### 1. 查看详细日志
打开微信开发者工具的控制台，查看：
- API请求URL
- API请求参数
- API响应状态码
- API响应数据

### 2. 分步测试
1. 先测试API连接
2. 再测试微信登录获取code
3. 最后测试完整登录流程

### 3. 后端调试
在后端API文件中添加调试日志：
```php
error_log("收到请求: " . file_get_contents('php://input'));
error_log("微信AppID: " . WECHAT_APPID);
```

## 下一步操作

1. **立即测试**: 使用更新后的代码进行测试
2. **查看日志**: 重点关注控制台的API请求和响应日志
3. **检查配置**: 确认后端微信配置是否正确
4. **联系后端**: 如果API持续返回400，需要检查后端配置

## 预期结果

修复后，你应该看到：
1. API连接测试成功
2. 微信登录能获取到code
3. 如果账号未绑定，会提示需要绑定
4. 如果账号已绑定，会直接登录成功
